import { WatchTenantCollection as WatchTenantCollection_1d0591e3cf4f332c83a86da13a0de59a } from '@payloadcms/plugin-multi-tenant/client'
import { TenantField as TenantField_1d0591e3cf4f332c83a86da13a0de59a } from '@payloadcms/plugin-multi-tenant/client'
import { default as default_9664b5c5e4d42667ca5d06d7235f8318 } from '../../../collections/Assignments/components/LanguageCell'
import { default as default_af436a7f28339def9101927df70c8a67 } from '../../../collections/Assignments/components/DifficultyCell'
import { default as default_732e0f1fe1606e50eee0e58082d636fe } from '../../../collections/Assignments/components/GenerateContentButton'
import { default as default_06f4c3b672890382dd71c92e2e6fcf83 } from '../../../collections/Assignments/components/GoToPlaygroundButton'
import { default as default_6f5df42e6677d4ec54ed3226c3b43e5e } from '../../../collections/Assignments/components/DownloadReportButton'
import { default as default_8f4821694ece98d2377bbf57350f34c8 } from '../../../collections/Submissions/components/ActionsCell'
import { default as default_b14fe712a86d57243bd66ef363d77614 } from '../../../collections/Submissions/components/IdCell'
import { StatusCell as StatusCell_6a9f7b3f48f1a8f5edffdf257b3f0deb } from '../../../collections/Submissions/components/CustomCells'
import { ScoreCell as ScoreCell_6a9f7b3f48f1a8f5edffdf257b3f0deb } from '../../../collections/Submissions/components/CustomCells'
import { default as default_5acfac8e638133b6d35c57dad39c833d } from '../../../collections/Submissions/components/TestsResultLabel'
import { default as default_358916e0188c0b6a6de70b1011bcd7b7 } from '../../../collections/Submissions/components/TestsResultRowLabel'
import { default as default_b872f648245086eb4ff4258fab9f82db } from '../../../collections/Submissions/components/EvaluateButton'
import { default as default_32d15b2b13e1e9f604c74e03d9ff8022 } from '../../../components/Icon'
import { default as default_2396ffa5fcb8566707b31b0d1917b213 } from '../../../components/Logo'
import { default as default_6c2454bd5a0bbd17841c14c046f2df41 } from '../../../components/CustomNav'
import { default as default_ed8165bde93eabb05f8473a4d0074a61 } from '../../../components/AfterNavLinks'
import { TenantSelector as TenantSelector_1d0591e3cf4f332c83a86da13a0de59a } from '@payloadcms/plugin-multi-tenant/client'
import { TenantSelectionProvider as TenantSelectionProvider_d6d5f193a167989e2ee7d14202901e62 } from '@payloadcms/plugin-multi-tenant/rsc'
import { default as default_f5c2d4b228fb0b8ba01a5da29f72e7f2 } from '../../../components/pending-submissions/index'

export const importMap = {
  "@payloadcms/plugin-multi-tenant/client#WatchTenantCollection": WatchTenantCollection_1d0591e3cf4f332c83a86da13a0de59a,
  "@payloadcms/plugin-multi-tenant/client#TenantField": TenantField_1d0591e3cf4f332c83a86da13a0de59a,
  "./collections/Assignments/components/LanguageCell#default": default_9664b5c5e4d42667ca5d06d7235f8318,
  "./collections/Assignments/components/DifficultyCell#default": default_af436a7f28339def9101927df70c8a67,
  "./collections/Assignments/components/GenerateContentButton#default": default_732e0f1fe1606e50eee0e58082d636fe,
  "./collections/Assignments/components/GoToPlaygroundButton#default": default_06f4c3b672890382dd71c92e2e6fcf83,
  "./collections/Assignments/components/DownloadReportButton#default": default_6f5df42e6677d4ec54ed3226c3b43e5e,
  "./collections/Submissions/components/ActionsCell#default": default_8f4821694ece98d2377bbf57350f34c8,
  "./collections/Submissions/components/IdCell#default": default_b14fe712a86d57243bd66ef363d77614,
  "./collections/Submissions/components/CustomCells#StatusCell": StatusCell_6a9f7b3f48f1a8f5edffdf257b3f0deb,
  "./collections/Submissions/components/CustomCells#ScoreCell": ScoreCell_6a9f7b3f48f1a8f5edffdf257b3f0deb,
  "./collections/Submissions/components/TestsResultLabel#default": default_5acfac8e638133b6d35c57dad39c833d,
  "./collections/Submissions/components/TestsResultRowLabel#default": default_358916e0188c0b6a6de70b1011bcd7b7,
  "./collections/Submissions/components/EvaluateButton#default": default_b872f648245086eb4ff4258fab9f82db,
  "./components/Icon#default": default_32d15b2b13e1e9f604c74e03d9ff8022,
  "./components/Logo#default": default_2396ffa5fcb8566707b31b0d1917b213,
  "./components/CustomNav#default": default_6c2454bd5a0bbd17841c14c046f2df41,
  "./components/AfterNavLinks#default": default_ed8165bde93eabb05f8473a4d0074a61,
  "@payloadcms/plugin-multi-tenant/client#TenantSelector": TenantSelector_1d0591e3cf4f332c83a86da13a0de59a,
  "@payloadcms/plugin-multi-tenant/rsc#TenantSelectionProvider": TenantSelectionProvider_d6d5f193a167989e2ee7d14202901e62,
  "./components/pending-submissions/index#default": default_f5c2d4b228fb0b8ba01a5da29f72e7f2
}
