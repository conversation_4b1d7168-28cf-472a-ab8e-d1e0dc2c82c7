import { Agent, createStep, createWorkflow } from '@mastra/core';
import z from 'zod';
import { getModel } from '../config';

const inputSchema = z.object({
  assignment: z.object({
    title: z.string(),
    description: z.string(),
    testsRequirement: z.string(),
  }),
  sourceCode: z.string(),
});

const outputSchema = z.object({
  testcases: z
    .array(
      z.object({
        input: z.string().describe('Input string for the program'),
        expectedOutput: z.string().describe('Complete expected stdout output'),
      }),
    )
    .min(1, 'Must generate at least one test case')
    .describe('Array of test cases for validation'),
});

const demoAgent = new Agent({
  name: 'demoAgent',
  instructions:
    'You receive a single string. Split it on spaces, then put every word whose length is even into evenLengthWords, and every word whose length is odd into oddLengthWords.',
  model: getModel(false),
});

const demoStep = createStep({
  id: 'demoStep',
  inputSchema,
  outputSchema,
  execute: async ({ inputData: { assignment, sourceCode } }) => {
    const response = await demoAgent.generate(
      [
        {
          role: 'user',
          content:
            `<assignment><title>${assignment.title}</title><description>${assignment.description}</description></assignment><testsRequirement>${assignment.testsRequirement?.trim()}</testsRequirement><sourceCode>${sourceCode.trim()}</sourceCode>`.trim(),
        },
      ],
      {
        output: outputSchema,
      },
    );
    return response.object;
  },
});

export const demoWorkflow = createWorkflow({
  id: 'demo-workflow',
  inputSchema,
  outputSchema,
}).then(demoStep);

demoWorkflow.commit();
